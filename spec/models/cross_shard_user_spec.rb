# frozen_string_literal: true

require 'rails_helper'
RSpec.describe CrossShardUser, type: :model do
  context 'across shards' do
    with_multiple_shards(
      shards: { alt: 'stride_k5_dashboard_test_alternate' },
      tenant_names: %w[primary alt:alt]
    )

    before :context do
      create(:organization, name: 'primary', shard: 'default', canvas_shard_id: 1)
      create(:organization, name: 'alt', shard: 'alt', canvas_shard_id: 2)
    end

    let!(:org1) { PandaPal::Organization.find_by(name: 'primary') }
    let!(:org2) { PandaPal::Organization.find_by(name: 'alt') }

    before do
      org1.switch_tenant
      create(:user, canvas_id: 1)
      UserShardAssociation.create(user: User.last, organization: org2)

      org2.switch_tenant
      create(:user, canvas_id: 1 + PandaPal::Organization::SHARD_OFFSET)
    end

    describe '#shadow_record?' do
      it 'returns true if the user is a shadow record' do
        org2.switch_tenant
        expect(User.last.shadow_record?).to be true
      end

      it 'returns false if the user is not a shadow record' do
        org1.switch_tenant
        expect(User.last.shadow_record?).to be false
      end
    end

    describe '#each_related_organization' do
      it 'returns all organizations the user is a member of' do
        org2.switch_tenant
        expect(User.count).to eq(1)
        expect(User.last.each_related_organization.count).to eq(2)
      end

      it "doesn't work without UserShardAssociation records" do
        org1.switch_tenant
        UserShardAssociation.delete_all
        expect(User.last.each_related_organization.count).to eq(1)
      end
    end

    describe '#against_shards' do
      before do
        [org1, org2].each do |org|
          org.switch_tenant
          create(:enrollment, user: User.last)
        end
      end

      it 'queries against each organization the user is a member of' do
        uids = User.last.against_shards do |user|
          Enrollment.where(user: user).pluck(:canvas_user_id)
        end.flatten
        expect(uids).to eq([1, 1 + PandaPal::Organization::SHARD_OFFSET])
      end

      it 'find_each queries against each organization the user is a member of' do
        all_enrs = User.last.against_shards(->(u) { Enrollment.where(user: u) }).to_a
        expect(all_enrs.count).to eq(2)
        expect(all_enrs.map(&:canvas_user_id)).to eq([1, 1 + PandaPal::Organization::SHARD_OFFSET])
      end

      it 'supports optional org argument in block (1-arity)' do
        results = []
        User.last.against_shards do |user|
          results << Enrollment.where(user: user).pluck(:canvas_user_id)
        end

        expect(results.flatten).to eq([1, 1 + PandaPal::Organization::SHARD_OFFSET])
      end

      it 'supports block with user and org (2-arity)' do
        results = []
        User.last.against_shards do |user, org|
          canvas_ids = Enrollment.where(user: user).pluck(:canvas_user_id)
          results << { org: org.name, canvas_ids: canvas_ids }
        end

        expect(results).to contain_exactly(
          { org: 'primary', canvas_ids: [1] },
          { org: 'alt',     canvas_ids: [1 + PandaPal::Organization::SHARD_OFFSET] }
        )
      end
    end
  end
end
