# frozen_string_literal: true

require 'fakeredis'

RSpec.configure do |config|
  config.before(:suite) do
    # Configure FakeRedis for tests
    Redis.current = Redis.new(url: 'redis://localhost:6379/15')
  end

  config.before do
    # Clear Redis data before each test
    begin
      Redis.current.flushdb
    rescue => e
      # Ignore Redis connection errors in tests
      Rails.logger.debug "Redis error in test: #{e.message}"
    end
  end

  config.after(:suite) do
    # Clean up after all tests
    begin
      Redis.current.flushdb
    rescue => e
      # Ignore Redis connection errors in tests
      Rails.logger.debug "Redis error in test cleanup: #{e.message}"
    end
  end
end

# Helper methods for testing Redis functionality
module RedisTestHelpers
  def redis_client
    Redis.current
  end

  def clear_redis
    begin
      Redis.current.flushdb
    rescue => e
      Rails.logger.debug "Redis error in clear_redis: #{e.message}"
    end
  end

  def redis_keys
    begin
      Redis.current.keys('*')
    rescue => e
      Rails.logger.debug "Redis error in redis_keys: #{e.message}"
      []
    end
  end

  def redis_get(key)
    begin
      Redis.current.get(key)
    rescue => e
      Rails.logger.debug "Redis error in redis_get: #{e.message}"
      nil
    end
  end

  def redis_set(key, value)
    begin
      Redis.current.set(key, value)
    rescue => e
      Rails.logger.debug "Redis error in redis_set: #{e.message}"
      false
    end
  end
end

RSpec.configure do |config|
  config.include RedisTestHelpers
end
