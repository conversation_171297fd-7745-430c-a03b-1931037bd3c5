# frozen_string_literal: true

require 'mock_redis'

RSpec.configure do |config|
  config.before(:suite) do
    # Set up MockRedis for the entire test suite
    @mock_redis = MockRedis.new

    # Stub Redis.new to return our mock
    allow(Redis).to receive(:new).and_return(@mock_redis)
  end

  config.before(:each) do
    # Clear Redis data before each test
    @mock_redis.flushdb if @mock_redis
  end

  config.after(:suite) do
    # Clean up after all tests
    @mock_redis.flushdb if @mock_redis
  end
end

# Helper methods for testing Redis functionality
module RedisTestHelpers
  def redis_client
    @mock_redis
  end

  def clear_redis
    @mock_redis&.flushdb
  end

  def redis_keys
    @mock_redis&.keys('*')
  end

  def redis_get(key)
    @mock_redis&.get(key)
  end

  def redis_set(key, value)
    @mock_redis&.set(key, value)
  end
end

RSpec.configure do |config|
  config.include RedisTestHelpers
end
