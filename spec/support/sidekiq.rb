# frozen_string_literal: true

require 'sidekiq/testing'

RSpec.configure do |config|
  config.before(:suite) do
    # Use fake mode for Sidekiq in tests - jobs are stored in arrays instead of Redis
    Sidekiq::Testing.fake!
  end

  config.before do
    # Clear all jobs before each test
    Sidekiq::Worker.clear_all
  end

  config.after(:suite) do
    # Reset Sidekiq testing mode after tests
    Sidekiq::Testing.disable!
  end
end

# Helper methods for testing Sidekiq jobs
module SidekiqTestHelpers
  def clear_sidekiq_jobs
    Sidekiq::Worker.clear_all
  end

  def sidekiq_jobs_count(worker_class = nil)
    if worker_class
      worker_class.jobs.size
    else
      Sidekiq::Worker.jobs.values.flatten.size
    end
  end

  def sidekiq_jobs_for(worker_class)
    worker_class.jobs
  end

  def perform_sidekiq_jobs
    Sidekiq::Worker.drain_all
  end

  def perform_sidekiq_jobs_for(worker_class)
    worker_class.drain
  end

  def expect_sidekiq_job(worker_class, args: nil, count: 1)
    jobs = sidekiq_jobs_for(worker_class)
    expect(jobs.size).to eq(count)
    
    if args
      expect(jobs.last['args']).to eq(args)
    end
  end

  def expect_no_sidekiq_jobs(worker_class = nil)
    if worker_class
      expect(sidekiq_jobs_for(worker_class)).to be_empty
    else
      expect(sidekiq_jobs_count).to eq(0)
    end
  end
end

RSpec.configure do |config|
  config.include SidekiqTestHelpers
end
