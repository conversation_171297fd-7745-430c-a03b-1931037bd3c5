require 'sidekiq'
require 'sidekiq-scheduler'

Rails.application.config.active_job.queue_adapter = :sidekiq

# Use FakeRedis in test environment
if Rails.env.test?
  require 'fakeredis'

  Sidekiq.configure_server do |config|
    config.redis = { url: 'redis://localhost:6379/15' }
    config.server_middleware do |chain|
      chain.add Apartment::Sidekiq::Middleware::Server
      chain.add Sidekiq::Batch::Server
      # chain.add WorkerCallbacks::Middleware::Server
    end
  end

  Sidekiq.configure_client do |config|
    config.redis = { url: 'redis://localhost:6379/15' }
  end
else
  Sidekiq.configure_server do |config|
    config.redis = { url: ENV["REDIS_URL"] || 'redis://localhost:6379' }
    config.server_middleware do |chain|
      chain.add Apartment::Sidekiq::Middleware::Server
      chain.add Sidekiq::Batch::Server
      # chain.add WorkerCallbacks::Middleware::Server
    end
  end

  Sidekiq.configure_client do |config|
    config.redis = { url: ENV["REDIS_URL"] || 'redis://localhost:6379' }
  end
end
