# frozen_string_literal: true

class CanvasSyncStarterWorker
  include Sidekiq::Job

  def perform(_opts = {})
    api_acc = canvas_sync_client.account('self', includes: ['global_id'])
    current_organization.tap do |org|
      org.canvas_shard_id = (api_acc[:global_id] / PandaPal::Organization::SHARD_OFFSET).floor
      org.settings[:canvas][:default_time_zone] = api_acc[:default_time_zone]
      org.save!
    end

    models = %w[roles accounts courses users pseudonyms enrollments user_observers admins terms]
    job_chain = CanvasSync.default_provisioning_report_chain(
      models,
      options: {},
      term_scope: 'active',
      updated_after: Rails.env.development? ? false : nil
    )
    job_chain.insert({ job: RefreshCrossShardUsersJob })

    job_chain.process!
  end
end
