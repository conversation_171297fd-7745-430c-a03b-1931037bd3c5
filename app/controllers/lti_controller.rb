# frozen_string_literal: true

class LtiController < ApplicationController
  before_action :set_js_env

  rescue_from CanCan::AccessDenied, with: :handle_access_denied

  def account_navigation
    authorize! :launch_from, :account
    render component: 'UnAuthorized', prerender: false
  end

  def course_navigation
    authorize! :launch_from, :course
    render component: 'UnAuthorized', prerender: false
  end

  private

  def set_js_env
    js_env({
             launch_point: action_name
           })
  end

  def handle_access_denied
    respond_to do |format|
      format.html { render component: 'UnAuthorized', prerender: false, status: :forbidden }
      format.json { render json: { error: 'Unauthorized' }, status: :unauthorized }
      format.any  { head :unauthorized }
    end
  end
end
