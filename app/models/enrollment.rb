# frozen_string_literal: true

# #
# AUTO GENERATED MODEL
# This model was auto generated by the CanvasSync Gem.
# You can customize it as needed, but make sure you test
# any changes you make to the auto generated methods.
#

class Enrollment < ApplicationRecord
  include CanvasSync::Record
  include CanvasSync::Concerns::ApiSyncable

  canvas_sync_features :defaults

  # include CanvasSync::Concerns::LiveEventSync
  # after_process_live_event do
  #   if user.nil?
  #     u = User.new(canvas_id: canvas_user_id)
  #     u.sync_from_api
  #   end
  #   if course.nil?
  #     c = Course.new(canvas_id: canvas_course_id)
  #     c.sync_from_api
  #   end
  # end

  validates :canvas_id, uniqueness: true, presence: true
  belongs_to :user, primary_key: :canvas_id, foreign_key: :canvas_user_id, optional: true
  belongs_to :role, primary_key: :canvas_id, foreign_key: :canvas_role_id, optional: true
  belongs_to :course, primary_key: :canvas_id, foreign_key: :canvas_course_id, optional: true
  belongs_to :section, primary_key: :canvas_id, foreign_key: :canvas_section_id, optional: true

  scope :active, -> { where(workflow_state: %w[active concluded]) }
  scope :teacher, -> { where(base_role_type: 'TeacherEnrollment') }
  scope :student, -> { where(base_role_type: 'StudentEnrollment') }
  scope :observer, -> { where(base_role_type: 'ObserverEnrollment') }
  scope :ta, -> { where(base_role_type: 'TaEnrollment') }
  scope :designer, -> { where(base_role_type: 'DesignerEnrollment') }

  api_syncable({
                 canvas_id: :id,
                 canvas_course_id: :course_id,
                 course_sis_id: :sis_course_id,
                 canvas_user_id: :user_id,
                 user_sis_id: :sis_user_id,
                 canvas_section_id: :course_section_id,
                 role: :role,
                 canvas_role_id: :role_id,
                 base_role_type: :type,
                 workflow_state: :enrollment_state
               }, ->(api) { api.enrollment('self', canvas_id) })
end
