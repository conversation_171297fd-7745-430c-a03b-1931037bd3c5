# frozen_string_literal: true

module OrganizationExtension
  extend ActiveSupport::Concern

  SHARD_OFFSET = 10_000_000_000_000

  included do
    scheduled_task '0 */6 * * *', :sync_canvas, worker: CanvasSyncStarterWorker

    before_create do
      self.shard ||= Apartment.shard_configurations.keys.sample || 'default' unless Rails.env.test?
    end

    after_lti_install do
      settings[:canvas][:external_tool_id] = @new_lti_installation[:id]
    end
  end

  class_methods do
    def for_canvas_shard(cid)
      cid = (cid / SHARD_OFFSET).floor if cid > SHARD_OFFSET
      find_by(canvas_shard_id: cid)
    end
  end
end
